name: Pull Request CI

on:
  pull_request:
    branches:
      - develop
      - 'feat/**'

# TODO: Configure the following secrets in repository settings:
# SNYK_TOKEN, SONAR_TOKEN, SLACK_WEBHOOK_URL
env:
  SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
  SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
  SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }} # Added for Slack notification

jobs:
  check_pr_title:
    name: Check PR Title
    runs-on: ubuntu-latest
    steps:
      - name: Validate PR Title
        uses: amannn/action-semantic-pull-request@v5
        with:
          # By default, it allows all conventional commit types.
          # We can specify scopes or disallow certain types if needed by the project.
          # For now, default types are sufficient.
          # Ensure the action has access to the PR title.
          # The GITHUB_TOKEN is implicitly provided and used by the action.
          # Add a subjectPattern to enforce a maximum length for the subject line (e.g. 72 characters)
          subjectPattern: ^.{1,72}$ # Example: Enforce subject length (optional but good practice)
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  main_ci_pipeline:
    name: Main CI Pipeline
    runs-on: ubuntu-latest
    needs: check_pr_title # Ensures this job runs after check_pr_title succeeds
    steps:
      - name: Checkout PR Branch (HEAD)
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }} # Checkout the actual PR commit
          fetch-depth: 0 # Fetch all history for all branches and tags

      - name: Setup Git User
        run: |
          git config --global user.name "GitHub Actions"
          git config --global user.email "<EMAIL>"

      - name: Fetch Target Branch and Merge
        run: |
          echo "Fetching target branch: ${{ github.base_ref }}"
          git fetch origin "${{ github.base_ref }}":"${{ github.base_ref }}" # Fetch the target branch specifically and create a local branch
          echo "Merging target branch (${{ github.base_ref }}) into PR branch (${{ github.head_ref }})"
          # Merge the local branch that now tracks the target branch
          git merge --no-ff --no-edit "${{ github.base_ref }}" || {
            echo "Merge conflict detected. Please resolve conflicts locally."
            exit 1
          }
          echo "Merge successful."

      - name: Verify Merge and Current State
        run: |
          echo "Current branch and last commit after merge:"
          git rev-parse --abbrev-ref HEAD
          git log -1 --oneline
          echo "Full git status:"
          git status
          echo "Branches:"
          git branch -a

      - name: Set up JDK 17 and Cache Gradle dependencies
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'temurin'
          cache: 'gradle' # This option handles caching of ~/.gradle/caches and ~/.gradle/wrapper

      - name: Make Gradle Wrapper Executable
        run: chmod +x ./gradlew

      - name: Check Gradle Dependency Sorting
        run: ./gradlew checkDependencySorting # Placeholder: Replace with actual task if different
        # Common alternatives could be:
        # ./gradlew lintGradle
        # ./gradlew dependencyUpdates -Drevision=release -DoutputFormatter=plain --no-build-cache (and then check output)
        # Or a custom script provided by the repository.
        # For now, 'checkDependencySorting' is a placeholder.

      - name: Find Affected Modules
        id: affected_modules
        run: |
          # Ensure base.sha is actually fetched. fetch-depth: 0 should make it available.
          CHANGED_FILES=$(git diff --name-only --diff-filter=ACMRTUXB ${{ github.event.pull_request.base.sha }} ${{ github.event.pull_request.head.sha }})

          AFFECTED_MODULES_LIST=""
          RUN_ON_ALL_MODULES="false"
          NO_MODULES_AFFECTED="false" # Default to false

          if [ -z "$CHANGED_FILES" ]; then
            echo "No files changed in the PR."
            NO_MODULES_AFFECTED="true"
          else
            echo "Changed files:"
            echo "$CHANGED_FILES"

            # Check for root build file changes
            ROOT_FILES_PATTERN="^(build\.gradle\.kts|settings\.gradle\.kts|gradle/libs\.versions\.toml|buildSrc/|gradlew|gradle/wrapper/)"
            if echo "$CHANGED_FILES" | grep -qE "$ROOT_FILES_PATTERN"; then
              echo "Root build configuration or Gradle wrapper changed, flagging to run on all modules."
              RUN_ON_ALL_MODULES="true"
            else
              MODULE_SET="" # Using a string to collect unique modules, comma-separated
              for FILE_PATH in $CHANGED_FILES; do
                MODULE_NAME=""
                if [[ "$FILE_PATH" == core/* ]]; then MODULE_NAME=":core";
                elif [[ "$FILE_PATH" == app/* ]]; then MODULE_NAME=":app";
                elif [[ "$FILE_PATH" == android/* ]]; then MODULE_NAME=":android";
                elif [[ "$FILE_PATH" == kotlin-jvm-app/* ]]; then MODULE_NAME=":kotlin-jvm-app";
                elif [[ "$FILE_PATH" == integrations/* ]]; then
                  INTEGRATION_NAME=$(echo "$FILE_PATH" | cut -d/ -f2)
                  MODULE_NAME=":integrations:$INTEGRATION_NAME"
                fi

                if [ -n "$MODULE_NAME" ]; then
                  # Add to set if not already present
                  if [[ ! ",${MODULE_SET}," == *",${MODULE_NAME},"* ]]; then
                    if [ -z "$MODULE_SET" ]; then MODULE_SET="$MODULE_NAME"; else MODULE_SET="${MODULE_SET},${MODULE_NAME}"; fi
                  fi
                fi
              done
              AFFECTED_MODULES_LIST="$MODULE_SET"
              if [ -z "$AFFECTED_MODULES_LIST" ]; then
                echo "No specific library/application modules were affected by the changes (e.g., only docs, tests in non-module dirs)."
                NO_MODULES_AFFECTED="true"
              fi
            fi
          fi

          echo "Run on all modules: $RUN_ON_ALL_MODULES"
          echo "Affected modules list: $AFFECTED_MODULES_LIST"
          echo "No modules affected: $NO_MODULES_AFFECTED"

          echo "affected_modules_list=$AFFECTED_MODULES_LIST" >> $GITHUB_OUTPUT
          echo "run_on_all_modules=$RUN_ON_ALL_MODULES" >> $GITHUB_OUTPUT
          echo "no_modules_affected=$NO_MODULES_AFFECTED" >> $GITHUB_OUTPUT

          # Also set as ENV vars for easier access in subsequent script steps if needed
          echo "AFFECTED_MODULES_LIST_ENV=$AFFECTED_MODULES_LIST" >> $GITHUB_ENV
          echo "RUN_ON_ALL_MODULES_ENV=$RUN_ON_ALL_MODULES" >> $GITHUB_ENV
          echo "NO_MODULES_AFFECTED_ENV=$NO_MODULES_AFFECTED" >> $GITHUB_ENV
        shell: bash

      - name: Setup Snyk CLI
        uses: snyk/actions/setup@0767748f184327900430fd0455098274cb997788 # Pinned to a specific commit hash for security
        # The SNYK_TOKEN environment variable defined at the job level will be used by Snyk.

      - name: Run Snyk to Check for Vulnerabilities
        run: |
          # Scan all detected projects and report vulnerabilities of high severity or above.
          # Fails the build if any such vulnerabilities are found.
          snyk test --all-projects --severity-threshold=high
          # For more detailed output or specific project types, flags like --print-deps or --gradle can be added.
          # e.g., snyk test --all-projects --severity-threshold=high --print-deps --gradle
          # The `snyk monitor` command is used for ongoing monitoring and reporting to the Snyk UI,
          # while `snyk test` is used here to test and potentially fail the CI build.
        env:
          # SNYK_TOKEN is inherited from the job's env context, but can be explicitly passed if needed:
          SNYK_TOKEN: ${{ env.SNYK_TOKEN }}

      - name: Run Detekt (Affected Modules Only)
        if: steps.affected_modules.outputs.no_modules_affected == 'false' # Only run if some modules are affected or all_modules is true
        run: |
          DETEKT_CMD="./gradlew"
          echo "Run on all modules flag: ${{ steps.affected_modules.outputs.run_on_all_modules }}"
          echo "Affected modules list: ${{ steps.affected_modules.outputs.affected_modules_list }}"

          if [[ "${{ steps.affected_modules.outputs.run_on_all_modules }}" == "true" ]]; then
            # If buildSrc changes, 'detekt' on all modules might be appropriate.
            # Assumes a top-level 'detekt' task runs it for all relevant modules including buildSrc if configured.
            DETEKT_CMD="${DETEKT_CMD} detekt"
            echo "Running Detekt on all modules."
          elif [[ -n "${{ steps.affected_modules.outputs.affected_modules_list }}" ]]; then
            MODULE_TASKS_STRING=""
            IFS=',' read -ra ADDR <<< "${{ steps.affected_modules.outputs.affected_modules_list }}"
            for i in "${ADDR[@]}"; do
              if [ -z "$MODULE_TASKS_STRING" ]; then
                MODULE_TASKS_STRING="${i}:detekt"
              else
                MODULE_TASKS_STRING="${MODULE_TASKS_STRING} ${i}:detekt"
              fi
            done
            DETEKT_CMD="${DETEKT_CMD} ${MODULE_TASKS_STRING}" # Note space before $MODULE_TASKS_STRING
            echo "Running Detekt on specific modules: ${MODULE_TASKS_STRING}"
          else
            # This case should be caught by the 'if' condition of the step.
            echo "No modules specified for Detekt and not running on all modules. Skipping Detekt."
            exit 0 # Exit successfully, do nothing
          fi

          echo "Executing: ${DETEKT_CMD}"
          ${DETEKT_CMD}
        shell: bash

      - name: Run Android Lint (Affected Modules Only, Exclude Core)
        if: steps.affected_modules.outputs.no_modules_affected == 'false'
        run: |
          LINT_CMD_PARTS=""
          echo "Run on all modules flag: ${{ steps.affected_modules.outputs.run_on_all_modules }}"
          echo "Affected modules list: ${{ steps.affected_modules.outputs.affected_modules_list }}"

          MODULE_LIST_TO_LINT=()

          if [[ "${{ steps.affected_modules.outputs.run_on_all_modules }}" == "true" ]]; then
            echo "Determining all projects to run Lint (excluding :core)..."
            # The -q flag is added to projects to make the output cleaner and parsing more reliable.
            ALL_PROJECTS=$(./gradlew -q projects | grep "Project ':" | sed "s/Project '\(.*\)'/\1/")
            echo "All projects found by './gradlew -q projects': ${ALL_PROJECTS}"
            for P_NAME in ${ALL_PROJECTS}; do
              if [[ "$P_NAME" != ":core" ]]; then
                MODULE_LIST_TO_LINT+=("$P_NAME")
              else
                echo "Excluding :core from Lint (when run_on_all_modules=true)"
              fi
            done
            echo "Modules to Lint (all except :core): ${MODULE_LIST_TO_LINT[*]}"
          elif [[ -n "${{ steps.affected_modules.outputs.affected_modules_list }}" ]]; then
            IFS=',' read -ra ADDR <<< "${{ steps.affected_modules.outputs.affected_modules_list }}"
            for i in "${ADDR[@]}"; do
              if [[ "$i" != ":core" ]]; then
                MODULE_LIST_TO_LINT+=("$i")
              else
                echo "Excluding :core from Lint (from affected_modules_list)"
              fi
            done
            echo "Modules to Lint (specific, excluding :core): ${MODULE_LIST_TO_LINT[*]}"
          fi

          if [ ${#MODULE_LIST_TO_LINT[@]} -eq 0 ]; then
            echo "No modules to run Lint on (either none affected, or only :core was affected/listed and is excluded)."
            exit 0
          fi

          for MODULE_NAME in "${MODULE_LIST_TO_LINT[@]}"; do
            # Ensure no leading/trailing spaces from module names if any parsing issues
            TRIMMED_MODULE_NAME=$(echo -n "${MODULE_NAME}" | xargs)
            if [ -n "$LINT_CMD_PARTS" ]; then
              LINT_CMD_PARTS="${LINT_CMD_PARTS} ${TRIMMED_MODULE_NAME}:lint"
            else
              LINT_CMD_PARTS="${TRIMMED_MODULE_NAME}:lint"
            fi
          done
          
          # Check if LINT_CMD_PARTS is empty, which can happen if all modules were :core.
          # This is redundant due to the check `if [ ${#MODULE_LIST_TO_LINT[@]} -eq 0 ]`, but kept for safety.
          if [ -z "$LINT_CMD_PARTS" ]; then
            echo "No lint tasks to run after filtering."
            exit 0
          fi

          echo "Executing: ./gradlew ${LINT_CMD_PARTS}"
          ./gradlew ${LINT_CMD_PARTS}
        shell: bash

      - name: Run Unit Tests (Affected Modules Only)
        if: steps.affected_modules.outputs.no_modules_affected == 'false'
        run: |
          TEST_CMD="./gradlew"
          echo "Run on all modules flag: ${{ steps.affected_modules.outputs.run_on_all_modules }}"
          echo "Affected modules list: ${{ steps.affected_modules.outputs.affected_modules_list }}"

          if [[ "${{ steps.affected_modules.outputs.run_on_all_modules }}" == "true" ]]; then
            # Run tests for all modules. The root 'test' task usually does this for all subprojects.
            TEST_CMD="${TEST_CMD} test" # For Android projects, this might be testDebugUnitTest or similar
                                      # 'test' is generally the standard for JVM modules and often for Android too.
            echo "Running unit tests on all modules."
          elif [[ -n "${{ steps.affected_modules.outputs.affected_modules_list }}" ]]; then
            MODULE_TASKS_STRING=""
            IFS=',' read -ra ADDR <<< "${{ steps.affected_modules.outputs.affected_modules_list }}"
            for i in "${ADDR[@]}"; do
              if [ -z "$MODULE_TASKS_STRING" ]; then
                MODULE_TASKS_STRING="${i}:test" # Or :testDebugUnitTest if consistently needed
              else
                MODULE_TASKS_STRING="${MODULE_TASKS_STRING} ${i}:test"
              fi
            done
            TEST_CMD="${TEST_CMD} ${MODULE_TASKS_STRING}"
            echo "Running unit tests on specific modules: ${MODULE_TASKS_STRING}"
          else
            echo "No modules specified for unit tests and not running on all modules. Skipping unit tests."
            exit 0 # Exit successfully, do nothing
          fi

          echo "Executing: ${TEST_CMD}"
          ${TEST_CMD}
        shell: bash

      - name: Run SonarCloud Analysis
        # This step runs if previous critical steps (like tests) passed.
        # SonarCloud typically analyzes the whole project.
        run: |
          ./gradlew sonar \
            -Dsonar.pullrequest.key=${{ github.event.pull_request.number }} \
            -Dsonar.pullrequest.branch=${{ github.event.pull_request.head.ref }} \
            -Dsonar.pullrequest.base=${{ github.event.pull_request.base.ref }}
        # SONAR_TOKEN is inherited from the workflow's env context.
        # sonar.projectKey, sonar.organization, sonar.host.url are expected in sonar-project.properties or build.gradle.
        shell: bash

      - name: Build SDK AAR and Sample App APK/AAB (Placeholder)
        run: |
          echo "SKIPPING: This is a placeholder for building the SDK AAR and Sample App APK/AAB."
          echo "Implementation needed for:"
          echo "1. Identify and build the SDK module(s) to produce an AAR."
          echo "   Example: ./gradlew :sdk_module:assembleRelease"
          echo "2. Make the AAR available to the 'Movies Sample app' (likely :app)."
          echo "   This could involve publishing to GitHub Packages temporarily or using a file-based dependency."
          echo "3. Build the 'Movies Sample app' (likely :app) to produce an APK or AAB."
          echo "   Example: ./gradlew :app:assembleRelease or :app:bundleRelease"
          echo "4. Upload the resulting APK/AAB to BrowserStack for testing."
          echo "   This requires BrowserStack credentials and integration (e.g., using BrowserStack GitHub Action or API)."
          echo "TODO: Fill in the actual commands and configurations for these sub-steps."
        shell: bash

  notify_slack:
    name: Notify Slack
    runs-on: ubuntu-latest
    if: always() # Ensures this job runs even if previous jobs fail
    needs: [main_ci_pipeline] # Depends on the completion of the main CI job
    steps:
      - name: Send Slack Notification
        uses: slackapi/slack-github-action@v1.26.0
        with:
          # Determine status based on the 'needs' context
          # The job name in 'needs.main_ci_pipeline.result' must exactly match the job name it depends on.
          # The actual job ID for main_ci_pipeline is 'main_ci_pipeline'.
          status: ${{ needs.main_ci_pipeline.result }}
          channel-id: '#sdk-ci-mobile' # As specified in the requirements
          # Custom message formatting
          # Using payload-file-path might be better for complex messages, but let's try with 'text' first.
          # Reference: https://github.com/slackapi/slack-github-action
          # The 'text' field is for the main message body.
          # For richer messages, 'blocks' are preferred.
          # Let's construct a simple text message first, then consider blocks if needed.
          # The action has inputs like `slack_message` for overrides, or uses a default.
          # Let's try to build a custom message.
          # The action's documentation shows it automatically includes a lot of context.
          # We can customize it using 'payload'.
          # For simplicity, let's rely on the default message structure and just set the status and channel.
          # The default message includes commit, PR link, workflow name, status, etc.

          # To customize message text with job status:
          # We need to check the result of the `main_ci_pipeline` job.
          # The `needs` context provides `needs.<job_id>.result`.
          # Let's construct a payload for more control.
          payload: |
            {
              "text": "CI Pipeline for PR #${{ github.event.pull_request.number }} finished with status: ${{ needs.main_ci_pipeline.result }}",
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "CI Pipeline for PR <${{ github.event.pull_request.html_url }}|#${{ github.event.pull_request.number }} - ${{ github.event.pull_request.title }}> finished."
                  }
                },
                {
                  "type": "section",
                  "fields": [
                    {
                      "type": "mrkdwn",
                      "text": "*Status:*\n${{ needs.main_ci_pipeline.result == 'success' && ':white_check_mark: Success' || ':x: Failure' }}"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "*Workflow:*\n${{ github.workflow }}"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "*Branch:*\n`${{ github.head_ref }}`"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "*Initiated by:*\n${{ github.actor }}"
                    }
                  ]
                },
                {
                  "type": "actions",
                  "elements": [
                    {
                      "type": "button",
                      "text": {
                        "type": "plain_text",
                        "text": "View Workflow Run"
                      },
                      "url": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                    }
                  ]
                }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }} # Already in global env, but can be explicit
          # GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }} # Some actions might need this, but slackapi/slack-github-action uses WEBHOOK_URL
